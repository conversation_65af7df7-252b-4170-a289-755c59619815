{"name": "server", "version": "1.0.4", "description": "", "main": "index.js", "scripts": {"start": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"axios": "^1.8.3", "bcrypt": "^5.1.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsons-pack": "^7.9.4", "jsonwebtoken": "^9.0.1", "mongoose": "^7.2.4", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.4", "nodemon": "^2.0.22", "request": "^2.88.2", "sqlite3": "^5.1.7", "stripe": "^12.2.0"}}